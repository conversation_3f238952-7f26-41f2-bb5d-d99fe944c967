import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Tenant } from '../model';
import { createTenantAPI, deleteTenantAPI, fetchTenantsAPI, updateTenantAPI, generateFirestoreConstraints } from '../api';
import type { QueryConstraint } from 'firebase/firestore';

export const useTenantStore = defineStore('tenant', () => {
    // State
    const tenants = ref<Tenant[]>([]);
    const isLoading = ref(false);
    const error = ref<string | null>(null);
    const totalRecords = ref(0);

    // Options for pagination, sorting, and filtering
    const options = ref({
        search: '',
        sortField: 'createdAt',
        sortOrder: -1, // 'desc'
        rows: 10, // Page size
        first: 0 // Starting index
    });

    // Computed value for Firestore query constraints
    const firestoreConstraints = computed(() => {
        return generateFirestoreConstraints(options.value);
    });

    // Actions
    const fetchTenants = async () => {
        isLoading.value = true;
        error.value = null;
        try {
            const fetchedTenants = await fetchTenantsAPI(firestoreConstraints.value as QueryConstraint[]);
            tenants.value = fetchedTenants;
            // Note: Firestore SDK does not directly return total count with a query.
            // A separate query would be needed for the total count, which can be costly.
            // For simplicity, we'll use the length of the fetched array.
            totalRecords.value = fetchedTenants.length;
        } catch (e: any) {
            error.value = e.message;
        } finally {
            isLoading.value = false;
        }
    };

    const createTenant = async (tenantData: Omit<Tenant, 'id'>): Promise<string | undefined> => {
        isLoading.value = true;
        try {
            const newTenant = await createTenantAPI(tenantData);
            await fetchTenants(); // Refresh list
            return newTenant.id;
        } catch (e: any) {
            error.value = e.message;
            throw e;
        } finally {
            isLoading.value = false;
        }
    };

    const updateTenant = async (id: string, tenantData: Partial<Tenant>) => {
        isLoading.value = true;
        try {
            await updateTenantAPI(id, tenantData);
            await fetchTenants(); // Refresh list
        } catch (e: any) {
            error.value = e.message;
            throw e;
        } finally {
            isLoading.value = false;
        }
    };

    const deleteTenant = async (id: string) => {
        isLoading.value = true;
        try {
            await deleteTenantAPI(id);
            await fetchTenants(); // Refresh list
        } catch (e: any) {
            error.value = e.message;
            throw e;
        } finally {
            isLoading.value = false;
        }
    };

    const approveTenant = async (id: string) => {
        isLoading.value = true;
        try {
            await updateTenantAPI(id, { approved: true });
            await fetchTenants(); // Refresh list
        } catch (e: any) {
            error.value = e.message;
            throw e;
        } finally {
            isLoading.value = false;
        }
    };

    const updateCompanyInfo = async (id: string, companyData: Partial<Tenant>) => {
        isLoading.value = true;
        try {
            // Add updatedAt timestamp
            const updateData = {
                ...companyData,
                updatedAt: new Date()
            };

            await updateTenantAPI(id, updateData);

            // Update the local tenant in the list if it exists
            const tenantIndex = tenants.value.findIndex(t => t.id === id);
            if (tenantIndex !== -1) {
                tenants.value[tenantIndex] = { ...tenants.value[tenantIndex], ...updateData };
            }
        } catch (e: any) {
            error.value = e.message;
            throw e;
        } finally {
            isLoading.value = false;
        }
    };

    // Function to update options and refetch tenants
    const setOptions = (newOptions: Partial<typeof options.value>) => {
        options.value = { ...options.value, ...newOptions };
        fetchTenants();
    };

    return {
        tenants,
        isLoading,
        error,
        totalRecords,
        options,
        fetchTenants,
        createTenant,
        updateTenant,
        deleteTenant,
        setOptions,
        approveTenant,
        updateCompanyInfo
    };
});
