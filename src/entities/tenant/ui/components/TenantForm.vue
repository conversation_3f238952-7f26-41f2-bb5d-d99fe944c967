<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue';
import type { Tenant } from '@/entities/tenant/model';
import {
  validateEmail,
  validateRequired,
  validatePhoneNumber,
  validateUrl,
  validatePostalCode,
  validateCompanyNumber,
  validateVatNumber,
  validateNumber
} from '@/utils';
import {
  INDUSTRY_OPTIONS,
  COMPANY_SIZE_OPTIONS,
  COUNTRY_OPTIONS,
  STATUS_OPTIONS,
  SUBSCRIPTION_PLAN_OPTIONS
} from '@/features/company-info-update/types';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';

// Component props
const props = defineProps<{
    visible: boolean;
    tenant: Partial<Tenant>;
    enhanced?: boolean; // New prop to enable enhanced fields
}>();

// Component emits
const emits = defineEmits(['update:visible', 'save']);

// Local state
const internalTenant = ref<Partial<Tenant>>({});
const submitted = ref(false);
const activeTab = ref(0);

// Enhanced errors object
const errors = ref({
    name: '',
    email: '',
    phone: '',
    website: '',
    industry: '',
    description: '',
    companyRegistrationNumber: '',
    vatNumber: '',
    city: '',
    state: '',
    postalCode: '',
    country: '',
    alternativePhone: '',
    supportEmail: '',
    linkedinUrl: '',
    twitterUrl: '',
    facebookUrl: '',
    foundedYear: ''
});

// Watch for changes in the tenant prop to update the local state
watch(
    () => props.tenant,
    (newVal) => {
        internalTenant.value = { ...newVal };
        submitted.value = false; // Reset submission state on new tenant
    },
    { deep: true, immediate: true }
);

// Enhanced validation functions
const validateField = (field: keyof typeof errors.value, value: string, validator: (val: string) => string) => {
    const error = validator(value);
    errors.value[field] = error;
    return !error;
};

const validateRequiredField = (field: keyof typeof errors.value, value: string) => {
    return validateField(field, value, validateRequired);
};

const validateEmailField = (field: keyof typeof errors.value, value: string) => {
    if (!value) {
        errors.value[field] = '';
        return true;
    }
    return validateField(field, value, validateEmail);
};

const validatePhoneField = (field: keyof typeof errors.value, value: string) => {
    if (!value) {
        errors.value[field] = '';
        return true;
    }
    return validateField(field, value, validatePhoneNumber);
};

const validateUrlField = (field: keyof typeof errors.value, value: string) => {
    if (!value) {
        errors.value[field] = '';
        return true;
    }
    return validateField(field, value, validateUrl);
};

const validateForm = () => {
    const validations = [
        validateRequiredField('name', internalTenant.value.name || ''),
        validateRequiredField('email', internalTenant.value.email || ''),
        validateEmailField('email', internalTenant.value.email || '')
    ];

    if (props.enhanced) {
        // Add enhanced validations
        validations.push(
            validatePhoneField('phone', internalTenant.value.phone || ''),
            validateUrlField('website', internalTenant.value.website || ''),
            validateUrlField('linkedinUrl', (internalTenant.value as any).linkedinUrl || ''),
            validateUrlField('twitterUrl', (internalTenant.value as any).twitterUrl || ''),
            validateUrlField('facebookUrl', (internalTenant.value as any).facebookUrl || ''),
            validateEmailField('supportEmail', (internalTenant.value as any).supportEmail || ''),
            validatePhoneField('alternativePhone', (internalTenant.value as any).alternativePhone || '')
        );

        // Validate postal code if provided
        if ((internalTenant.value as any).postalCode) {
            validations.push(validateField('postalCode', (internalTenant.value as any).postalCode, validatePostalCode));
        }

        // Validate company registration number if provided
        if ((internalTenant.value as any).companyRegistrationNumber) {
            validations.push(validateField('companyRegistrationNumber', (internalTenant.value as any).companyRegistrationNumber, validateCompanyNumber));
        }

        // Validate VAT number if provided
        if ((internalTenant.value as any).vatNumber) {
            validations.push(validateField('vatNumber', (internalTenant.value as any).vatNumber, validateVatNumber));
        }

        // Validate founded year if provided
        if ((internalTenant.value as any).foundedYear) {
            const currentYear = new Date().getFullYear();
            const yearStr = (internalTenant.value as any).foundedYear.toString();
            const yearError = validateNumber(yearStr);
            if (yearError) {
                errors.value.foundedYear = yearError;
                validations.push(false);
            } else {
                const year = parseInt(yearStr);
                if (year < 1800 || year > currentYear) {
                    errors.value.foundedYear = `Please enter a year between 1800 and ${currentYear}.`;
                    validations.push(false);
                } else {
                    errors.value.foundedYear = '';
                    validations.push(true);
                }
            }
        }
    }

    return validations.every(Boolean);
};

// Handle form submission
const saveTenant = () => {
    submitted.value = true;
    if (validateForm()) {
        emits('save', internalTenant.value);
    }
};

// Close the dialog
const closeDialog = () => {
    emits('update:visible', false);
};
</script>

<template>
    <Dialog
        :visible="visible"
        @update:visible="closeDialog"
        :style="{ width: enhanced ? '900px' : '450px' }"
        header="Tenant Details"
        :modal="true"
        class="p-fluid"
    >
        <!-- Simple Form (Original) -->
        <div v-if="!enhanced" class="flex flex-col gap-6">
            <div class="field">
                <label for="name" class="block font-bold mb-3">Name</label>
                <IconField>
                    <InputIcon class="pi pi-building" />
                    <InputText id="name" v-model.trim="internalTenant.name" required :class="{ 'p-invalid': errors.name && submitted }" autofocus fluid />
                </IconField>
                <small class="p-error" v-if="errors.name && submitted">{{ errors.name }}</small>
            </div>

            <div class="field">
                <label for="email" class="block font-bold mb-3">Email</label>
                <IconField>
                    <InputIcon class="pi pi-envelope" />
                    <InputText id="email" v-model.trim="internalTenant.email" required :class="{ 'p-invalid': errors.email && submitted }" fluid />
                </IconField>
                <small class="p-error" v-if="errors.email && submitted">{{ errors.email }}</small>
            </div>

            <div class="field">
                <label for="phone" class="block font-bold mb-3">Phone</label>
                <IconField>
                    <InputIcon class="pi pi-phone" />
                    <InputText id="phone" v-model.trim="internalTenant.phone" fluid />
                </IconField>
            </div>

            <div class="field">
                <label for="address" class="block font-bold mb-3">Address</label>
                <IconField>
                    <InputIcon class="pi pi-map-marker" />
                    <InputText id="address" v-model.trim="internalTenant.address" fluid />
                </IconField>
            </div>

            <div class="field">
                <label for="website" class="block font-bold mb-3">Website</label>
                <IconField>
                    <InputIcon class="pi pi-globe" />
                    <InputText id="website" v-model.trim="internalTenant.website" :class="{ 'p-invalid': errors.website && submitted }" fluid />
                </IconField>
                <small class="p-error" v-if="errors.website && submitted">{{ errors.website }}</small>
            </div>

            <div class="field">
                <label for="status" class="block font-bold mb-3">Status</label>
                <Dropdown id="status" v-model="internalTenant.status" :options="STATUS_OPTIONS" optionLabel="label" optionValue="value" placeholder="Select a Status" fluid />
            </div>

            <div class="field">
                <label for="subscriptionPlan" class="block font-bold mb-3">Subscription Plan</label>
                <Dropdown id="subscriptionPlan" v-model="internalTenant.subscriptionPlan" :options="SUBSCRIPTION_PLAN_OPTIONS" optionLabel="label" optionValue="value" placeholder="Select a Plan" fluid />
            </div>
        </div>

        <!-- Enhanced Form with Tabs -->
        <div v-else>
            <TabView v-model:activeIndex="activeTab" class="tenant-form-tabs">
                <!-- Basic Information Tab -->
                <TabPanel>
                    <template #header>
                        <div class="flex items-center gap-2">
                            <i class="pi pi-building"></i>
                            <span>Basic Info</span>
                        </div>
                    </template>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Company Name -->
                        <div class="field md:col-span-2">
                            <label for="name" class="block font-semibold mb-2">
                                Company Name <span class="text-red-500">*</span>
                            </label>
                            <IconField>
                                <InputIcon class="pi pi-building" />
                                <InputText
                                    id="name"
                                    v-model.trim="internalTenant.name"
                                    :class="{ 'p-invalid': errors.name && submitted }"
                                    placeholder="Enter company name"
                                    fluid
                                />
                            </IconField>
                            <small class="p-error" v-if="errors.name && submitted">{{ errors.name }}</small>
                        </div>

                        <!-- Industry -->
                        <div class="field">
                            <label for="industry" class="block font-semibold mb-2">Industry</label>
                            <Dropdown
                                id="industry"
                                v-model="internalTenant.industry"
                                :options="INDUSTRY_OPTIONS"
                                optionLabel="label"
                                optionValue="value"
                                placeholder="Select industry"
                                fluid
                            />
                        </div>

                        <!-- Company Size -->
                        <div class="field">
                            <label for="companySize" class="block font-semibold mb-2">Company Size</label>
                            <Dropdown
                                id="companySize"
                                v-model="internalTenant.companySize"
                                :options="COMPANY_SIZE_OPTIONS"
                                optionLabel="label"
                                optionValue="value"
                                placeholder="Select company size"
                                fluid
                            />
                        </div>

                        <!-- Status -->
                        <div class="field">
                            <label for="status" class="block font-semibold mb-2">Status</label>
                            <Dropdown
                                id="status"
                                v-model="internalTenant.status"
                                :options="STATUS_OPTIONS"
                                optionLabel="label"
                                optionValue="value"
                                placeholder="Select status"
                                fluid
                            />
                        </div>

                        <!-- Subscription Plan -->
                        <div class="field">
                            <label for="subscriptionPlan" class="block font-semibold mb-2">Subscription Plan</label>
                            <Dropdown
                                id="subscriptionPlan"
                                v-model="internalTenant.subscriptionPlan"
                                :options="SUBSCRIPTION_PLAN_OPTIONS"
                                optionLabel="label"
                                optionValue="value"
                                placeholder="Select plan"
                                fluid
                            />
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="field mt-4">
                        <label for="description" class="block font-semibold mb-2">Company Description</label>
                        <Textarea
                            id="description"
                            v-model="internalTenant.description"
                            placeholder="Brief description of your company..."
                            rows="3"
                            fluid
                        />
                    </div>
                </TabPanel>

                <!-- Contact Details Tab -->
                <TabPanel>
                    <template #header>
                        <div class="flex items-center gap-2">
                            <i class="pi pi-phone"></i>
                            <span>Contact</span>
                        </div>
                    </template>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Email -->
                        <div class="field">
                            <label for="email" class="block font-semibold mb-2">
                                Primary Email <span class="text-red-500">*</span>
                            </label>
                            <IconField>
                                <InputIcon class="pi pi-envelope" />
                                <InputText
                                    id="email"
                                    v-model.trim="internalTenant.email"
                                    :class="{ 'p-invalid': errors.email && submitted }"
                                    placeholder="<EMAIL>"
                                    type="email"
                                    fluid
                                />
                            </IconField>
                            <small class="p-error" v-if="errors.email && submitted">{{ errors.email }}</small>
                        </div>

                        <!-- Support Email -->
                        <div class="field">
                            <label for="supportEmail" class="block font-semibold mb-2">Support Email</label>
                            <IconField>
                                <InputIcon class="pi pi-headphones" />
                                <InputText
                                    id="supportEmail"
                                    v-model.trim="internalTenant.supportEmail"
                                    :class="{ 'p-invalid': errors.supportEmail && submitted }"
                                    placeholder="<EMAIL>"
                                    type="email"
                                    fluid
                                />
                            </IconField>
                            <small class="p-error" v-if="errors.supportEmail && submitted">{{ errors.supportEmail }}</small>
                        </div>

                        <!-- Phone -->
                        <div class="field">
                            <label for="phone" class="block font-semibold mb-2">Primary Phone</label>
                            <IconField>
                                <InputIcon class="pi pi-phone" />
                                <InputText
                                    id="phone"
                                    v-model.trim="internalTenant.phone"
                                    :class="{ 'p-invalid': errors.phone && submitted }"
                                    placeholder="+44 20 1234 5678"
                                    fluid
                                />
                            </IconField>
                            <small class="p-error" v-if="errors.phone && submitted">{{ errors.phone }}</small>
                        </div>

                        <!-- Alternative Phone -->
                        <div class="field">
                            <label for="alternativePhone" class="block font-semibold mb-2">Alternative Phone</label>
                            <IconField>
                                <InputIcon class="pi pi-mobile" />
                                <InputText
                                    id="alternativePhone"
                                    v-model.trim="internalTenant.alternativePhone"
                                    :class="{ 'p-invalid': errors.alternativePhone && submitted }"
                                    placeholder="+44 7123 456789"
                                    fluid
                                />
                            </IconField>
                            <small class="p-error" v-if="errors.alternativePhone && submitted">{{ errors.alternativePhone }}</small>
                        </div>

                        <!-- Address -->
                        <div class="field md:col-span-2">
                            <label for="address" class="block font-semibold mb-2">Address</label>
                            <IconField>
                                <InputIcon class="pi pi-map-marker" />
                                <InputText
                                    id="address"
                                    v-model.trim="internalTenant.address"
                                    placeholder="Street address"
                                    fluid
                                />
                            </IconField>
                        </div>

                        <!-- City -->
                        <div class="field">
                            <label for="city" class="block font-semibold mb-2">City</label>
                            <InputText
                                id="city"
                                v-model.trim="internalTenant.city"
                                placeholder="City"
                                fluid
                            />
                        </div>

                        <!-- Postal Code -->
                        <div class="field">
                            <label for="postalCode" class="block font-semibold mb-2">Postal Code</label>
                            <InputText
                                id="postalCode"
                                v-model.trim="internalTenant.postalCode"
                                :class="{ 'p-invalid': errors.postalCode && submitted }"
                                placeholder="Postal code"
                                fluid
                            />
                            <small class="p-error" v-if="errors.postalCode && submitted">{{ errors.postalCode }}</small>
                        </div>

                        <!-- Country -->
                        <div class="field md:col-span-2">
                            <label for="country" class="block font-semibold mb-2">Country</label>
                            <Dropdown
                                id="country"
                                v-model="internalTenant.country"
                                :options="COUNTRY_OPTIONS"
                                optionLabel="label"
                                optionValue="value"
                                placeholder="Select country"
                                fluid
                            />
                        </div>
                    </div>
                </TabPanel>

                <!-- Online Presence Tab -->
                <TabPanel>
                    <template #header>
                        <div class="flex items-center gap-2">
                            <i class="pi pi-globe"></i>
                            <span>Online</span>
                        </div>
                    </template>

                    <div class="grid grid-cols-1 gap-4">
                        <!-- Website -->
                        <div class="field">
                            <label for="website" class="block font-semibold mb-2">Website</label>
                            <IconField>
                                <InputIcon class="pi pi-globe" />
                                <InputText
                                    id="website"
                                    v-model.trim="internalTenant.website"
                                    :class="{ 'p-invalid': errors.website && submitted }"
                                    placeholder="https://www.example.com"
                                    type="url"
                                    fluid
                                />
                            </IconField>
                            <small class="p-error" v-if="errors.website && submitted">{{ errors.website }}</small>
                        </div>

                        <!-- LinkedIn URL -->
                        <div class="field">
                            <label for="linkedinUrl" class="block font-semibold mb-2">LinkedIn Profile</label>
                            <IconField>
                                <InputIcon class="pi pi-linkedin" />
                                <InputText
                                    id="linkedinUrl"
                                    v-model.trim="internalTenant.linkedinUrl"
                                    :class="{ 'p-invalid': errors.linkedinUrl && submitted }"
                                    placeholder="https://linkedin.com/company/example"
                                    type="url"
                                    fluid
                                />
                            </IconField>
                            <small class="p-error" v-if="errors.linkedinUrl && submitted">{{ errors.linkedinUrl }}</small>
                        </div>

                        <!-- Twitter URL -->
                        <div class="field">
                            <label for="twitterUrl" class="block font-semibold mb-2">Twitter Profile</label>
                            <IconField>
                                <InputIcon class="pi pi-twitter" />
                                <InputText
                                    id="twitterUrl"
                                    v-model.trim="internalTenant.twitterUrl"
                                    :class="{ 'p-invalid': errors.twitterUrl && submitted }"
                                    placeholder="https://twitter.com/example"
                                    type="url"
                                    fluid
                                />
                            </IconField>
                            <small class="p-error" v-if="errors.twitterUrl && submitted">{{ errors.twitterUrl }}</small>
                        </div>

                        <!-- Facebook URL -->
                        <div class="field">
                            <label for="facebookUrl" class="block font-semibold mb-2">Facebook Page</label>
                            <IconField>
                                <InputIcon class="pi pi-facebook" />
                                <InputText
                                    id="facebookUrl"
                                    v-model.trim="internalTenant.facebookUrl"
                                    :class="{ 'p-invalid': errors.facebookUrl && submitted }"
                                    placeholder="https://facebook.com/example"
                                    type="url"
                                    fluid
                                />
                            </IconField>
                            <small class="p-error" v-if="errors.facebookUrl && submitted">{{ errors.facebookUrl }}</small>
                        </div>
                    </div>
                </TabPanel>
            </TabView>
        </div>

        <template #footer>
            <Button label="Cancel" icon="pi pi-times" text @click="closeDialog"></Button>
            <Button label="Save" icon="pi pi-check" @click="saveTenant"></Button>
        </template>
    </Dialog>
</template>

<style scoped>
.p-error {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
}

.tenant-form-tabs :deep(.p-tabview-nav) {
    background: var(--surface-card);
    border-radius: 8px 8px 0 0;
}

.tenant-form-tabs :deep(.p-tabview-panels) {
    background: var(--surface-card);
    border-radius: 0 0 8px 8px;
    padding: 1.5rem;
}

.field {
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .grid-cols-1.md\\:grid-cols-2 {
        grid-template-columns: 1fr;
    }

    .md\\:col-span-2 {
        grid-column: span 1;
    }
}
</style>
