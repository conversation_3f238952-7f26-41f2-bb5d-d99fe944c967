<script setup lang="ts">
import { ref, watch, computed, defineProps, defineEmits } from 'vue';
import type { Tenant } from '@/entities/tenant/model';
import { validateEmail, validateRequired } from '@/utils';
import { CompanyInfoUpdate } from '@/features/company-info-update';
import { CompanyInfoService } from '@/features/company-info-update/services/companyInfoService';
import type { CompanyInfo } from '@/features/company-info-update/types';
import { STATUS_OPTIONS, SUBSCRIPTION_PLAN_OPTIONS } from '@/features/company-info-update/types';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';

// Component props
const props = defineProps<{
    visible: boolean;
    tenant: Partial<Tenant>;
    enhanced?: boolean; // Enable enhanced company information fields
}>();

// Component emits
const emits = defineEmits(['update:visible', 'save']);

// Local state
const internalTenant = ref<Partial<Tenant>>({});
const submitted = ref(false);
const errors = ref({
    name: '',
    email: ''
});

// Watch for changes in the tenant prop to update the local state
watch(
    () => props.tenant,
    (newVal) => {
        internalTenant.value = { ...newVal };
        submitted.value = false; // Reset submission state on new tenant
    },
    { deep: true, immediate: true }
);

// Convert tenant to company info format for enhanced mode
const companyInfo = computed(() => {
    if (!props.enhanced) return {};

    return {
        name: internalTenant.value.name,
        email: internalTenant.value.email,
        phone: internalTenant.value.phone || '',
        address: internalTenant.value.address || '',
        website: internalTenant.value.website,
        status: internalTenant.value.status || 'active',
        subscriptionPlan: internalTenant.value.subscriptionPlan || 'free',

        // Extract custom fields from tenant data
        industry: (internalTenant.value as any).industry,
        companySize: (internalTenant.value as any).companySize,
        description: (internalTenant.value as any).description,
        companyRegistrationNumber: (internalTenant.value as any).companyRegistrationNumber,
        vatNumber: (internalTenant.value as any).vatNumber,
        addressLine2: (internalTenant.value as any).addressLine2,
        city: (internalTenant.value as any).city,
        state: (internalTenant.value as any).state,
        postalCode: (internalTenant.value as any).postalCode,
        country: (internalTenant.value as any).country,
        alternativePhone: (internalTenant.value as any).alternativePhone,
        supportEmail: (internalTenant.value as any).supportEmail,
        linkedinUrl: (internalTenant.value as any).linkedinUrl,
        twitterUrl: (internalTenant.value as any).twitterUrl,
        facebookUrl: (internalTenant.value as any).facebookUrl,
        foundedYear: (internalTenant.value as any).foundedYear,
        timezone: (internalTenant.value as any).timezone
    } as CompanyInfo;
});

// Simple validation for basic mode
const validateForm = () => {
    errors.value.name = validateRequired(internalTenant.value.name || '');
    errors.value.email = validateEmail(internalTenant.value.email || '');
    return !errors.value.name && !errors.value.email;
};

// Handle form submission for simple mode
const saveTenant = () => {
    submitted.value = true;
    if (validateForm()) {
        emits('save', internalTenant.value);
    }
};

// Handle form submission for enhanced mode
const saveEnhancedTenant = (data: Partial<CompanyInfo>) => {
    // Convert company info back to tenant format
    const tenantData = CompanyInfoService.convertToTenantData(data);
    emits('save', tenantData);
};

// Close the dialog
const closeDialog = () => {
    emits('update:visible', false);
};
</script>

<template>
    <Dialog :visible="visible" @update:visible="closeDialog" :style="{ width: enhanced ? '1000px' : '450px' }" header="Tenant Details" :modal="true" class="p-fluid">
        <!-- Simple Form (Original) -->
        {{ internalTenant }}
        <div v-if="!enhanced" class="flex flex-col gap-6">
            <div class="field">
                <label for="name" class="block font-bold mb-3">Name</label>
                <IconField>
                    <InputIcon class="pi pi-building" />
                    <InputText id="name" v-model.trim="internalTenant.name" required :class="{ 'p-invalid': errors.name && submitted }" autofocus fluid />
                </IconField>
                <small class="p-error" v-if="errors.name && submitted">{{ errors.name }}</small>
            </div>

            <div class="field">
                <label for="email" class="block font-bold mb-3">Email</label>
                <IconField>
                    <InputIcon class="pi pi-envelope" />
                    <InputText id="email" v-model.trim="internalTenant.email" required :class="{ 'p-invalid': errors.email && submitted }" fluid />
                </IconField>
                <small class="p-error" v-if="errors.email && submitted">{{ errors.email }}</small>
            </div>

            <div class="field">
                <label for="phone" class="block font-bold mb-3">Phone</label>
                <IconField>
                    <InputIcon class="pi pi-phone" />
                    <InputText id="phone" v-model.trim="internalTenant.phone" fluid />
                </IconField>
            </div>

            <div class="field">
                <label for="address" class="block font-bold mb-3">Address</label>
                <IconField>
                    <InputIcon class="pi pi-map-marker" />
                    <InputText id="address" v-model.trim="internalTenant.address" fluid />
                </IconField>
            </div>

            <div class="field">
                <label for="website" class="block font-bold mb-3">Website</label>
                <IconField>
                    <InputIcon class="pi pi-globe" />
                    <InputText id="website" v-model.trim="internalTenant.website" fluid />
                </IconField>
            </div>

            <div class="field">
                <label for="status" class="block font-bold mb-3">Status</label>
                <Dropdown id="status" v-model="internalTenant.status" :options="STATUS_OPTIONS" optionLabel="label" optionValue="value" placeholder="Select a Status" fluid />
            </div>

            <div class="field">
                <label for="subscriptionPlan" class="block font-bold mb-3">Subscription Plan</label>
                <Dropdown id="subscriptionPlan" v-model="internalTenant.subscriptionPlan" :options="SUBSCRIPTION_PLAN_OPTIONS" optionLabel="label" optionValue="value" placeholder="Select a Plan" fluid />
            </div>
        </div>

        <!-- Enhanced Form using CompanyInfoUpdate component -->
        <div v-else class="enhanced-tenant-form">
            <CompanyInfoUpdate :company-info="companyInfo" :show-header="false" @save="saveEnhancedTenant" />
        </div>

        <template #footer>
            <Button label="Cancel" icon="pi pi-times" text @click="closeDialog"></Button>
            <Button label="Save" icon="pi pi-check" @click="saveTenant"></Button>
        </template>
    </Dialog>
</template>

<style scoped>
.p-error {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
}

.enhanced-tenant-form :deep(.company-info-form) {
    /* Adjust styling for dialog context */
    max-width: none;
    margin: 0;
}
</style>
