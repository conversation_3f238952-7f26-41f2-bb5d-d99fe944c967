<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useTenantStore } from '@/entities/tenant/store';
import type { Tenant } from '@/entities/tenant/model';
import type { User } from '@/entities/user';
import { useToast } from 'primevue/usetoast';
import { FilterMatchMode } from '@primevue/core/api';
import TenantForm from './TenantForm.vue';
import TenantUserForm from './TenantUserForm.vue';
import { storeToRefs } from 'pinia';
import { useAuthStore } from '@/entities/auth';

const toast = useToast();
const tenantStore = useTenantStore();
const authStore = useAuthStore();
const { tenants, isLoading, totalRecords, options } = storeToRefs(tenantStore);

const router = useRouter();
const tenantDialog = ref(false);
const adminUserDialog = ref(false);
const deleteTenantDialog = ref(false);
const deleteTenantsDialog = ref(false);
const tenant = ref<Partial<Tenant>>({});
const adminUser = ref<Partial<User>>({});
const newlyCreatedTenantId = ref<string | null>(null);
const selectedTenants = ref<Tenant[]>([]);
const submitted = ref(false);

const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS }
});

onMounted(() => {
    tenantStore.fetchTenants();
});

const openNew = () => {
    tenant.value = {};
    submitted.value = false;
    tenantDialog.value = true;
};

const editTenant = (prod: Tenant) => {
    tenant.value = { ...prod };
    tenantDialog.value = true;
};

const confirmDeleteTenant = (prod: Tenant) => {
    tenant.value = prod;
    deleteTenantDialog.value = true;
};

const deleteTenant = async () => {
    if (tenant.value.id) {
        await tenantStore.deleteTenant(tenant.value.id);
        deleteTenantDialog.value = false;
        tenant.value = {};
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Tenant Deleted', life: 3000 });
    }
};

const saveTenant = async (tenantData: Tenant) => {
    submitted.value = true;
    // Basic validation
    if (!tenantData.name?.trim() || !tenantData.email?.trim()) {
        return;
    }

    if (tenantData.id) {
        await tenantStore.updateTenant(tenantData.id, tenantData);
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Tenant Updated', life: 3000 });
        tenantDialog.value = false;
        tenant.value = {};
    } else {
        // New tenants created via UI are auto-approved
        const newTenantData = { ...tenantData, approved: true };
        const newTenantId = await tenantStore.createTenant(newTenantData as Omit<Tenant, 'id'>);
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Tenant Created', life: 3000 });
        tenantDialog.value = false;
        tenant.value = {};

        if (newTenantId) {
            newlyCreatedTenantId.value = newTenantId;
            adminUser.value = { role: 'tenant_admin', email: tenantData.email, tenantId: newTenantId }; // Pre-select the role
            adminUserDialog.value = true;
        }
    }
};

const saveAdminUser = async (userData: User) => {
    if (newlyCreatedTenantId.value) {
        await authStore.register({ ...userData, tenantId: newlyCreatedTenantId.value });
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Admin User Created', life: 3000 });
        adminUserDialog.value = false;
        adminUser.value = {};
        newlyCreatedTenantId.value = null;
    }
};

const approveTenant = async (tenantToApprove: Tenant) => {
    if (tenantToApprove.id) {
        await tenantStore.approveTenant(tenantToApprove.id);
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Tenant Approved', life: 3000 });
    }
};

const confirmDeleteSelected = () => {
    deleteTenantsDialog.value = true;
};

const deleteSelectedTenants = async () => {
    const promises = selectedTenants.value.map((t) => tenantStore.deleteTenant(t.id));
    await Promise.all(promises);
    deleteTenantsDialog.value = false;
    selectedTenants.value = [];
    toast.add({ severity: 'success', summary: 'Successful', detail: 'Tenants Deleted', life: 3000 });
};

const onPage = (event: any) => {
    options.value.first = event.first;
    options.value.rows = event.rows;
    tenantStore.fetchTenants();
};

const onSort = (event: any) => {
    options.value.sortField = event.sortField;
    options.value.sortOrder = event.sortOrder;
    tenantStore.fetchTenants();
};

const onFilter = () => {
    options.value.search = filters.value.global.value ?? '';
    tenantStore.fetchTenants();
};

const manageUsers = (tenantId: string) => {
    router.push(`/tenants/${tenantId}/users`);
};
</script>

<template>
    <div class="grid">
        <div class="col-12">
            <div class="card">
                <Toolbar class="mb-4">
                    <template v-slot:start>
                        <div class="my-2">
                            <Button label="New" icon="pi pi-plus" class="mr-2" severity="success" @click="openNew"></Button>
                            <Button label="Delete" icon="pi pi-trash" severity="danger" @click="confirmDeleteSelected" :disabled="!selectedTenants || !selectedTenants.length"></Button>
                        </div>
                    </template>
                </Toolbar>

                <DataTable
                    :value="tenants"
                    :lazy="true"
                    :paginator="true"
                    :rows="10"
                    v-model:filters="filters"
                    :totalRecords="totalRecords"
                    :loading="isLoading"
                    @page="onPage($event)"
                    @sort="onSort($event)"
                    @filter="onFilter"
                    v-model:selection="selectedTenants"
                    dataKey="id"
                    :globalFilterFields="['name', 'email', 'phone']"
                >
                    <template #header>
                        <div class="flex justify-between items-center">
                            <h5 class="m-0">Manage Tenants</h5>
                            <span class="p-input-icon-left">
                                <i class="pi pi-search" />
                                <InputText v-model="filters['global'].value" placeholder="Keyword Search" @keyup.enter="onFilter" />
                            </span>
                        </div>
                    </template>

                    <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>
                    <Column field="name" header="Name" :sortable="true">
                        <template #body="slotProps">
                            <div class="flex items-center gap-2">
                                <i class="pi pi-building"></i>
                                <span>{{ slotProps.data.name }}</span>
                            </div>
                        </template>
                    </Column>
                    <Column field="email" header="Email" :sortable="true">
                        <template #body="slotProps">
                            <div class="flex items-center gap-2">
                                <i class="pi pi-envelope"></i>
                                <span>{{ slotProps.data.email }}</span>
                            </div>
                        </template>
                    </Column>
                    <Column field="phone" header="Phone">
                        <template #body="slotProps">
                            <div class="flex items-center gap-2">
                                <i class="pi pi-phone"></i>
                                <span>{{ slotProps.data.phone }}</span>
                            </div>
                        </template>
                    </Column>
                    <Column field="approved" header="Approved" :sortable="true">
                        <template #body="slotProps">
                            <Tag :value="slotProps.data.approved ? 'Approved' : 'Pending'" :severity="slotProps.data.approved ? 'success' : 'warning'"></Tag>
                        </template>
                    </Column>
                    <Column field="status" header="Status" :sortable="true"></Column>
                    <Column field="subscriptionPlan" header="Plan" :sortable="true"></Column>
                    <Column headerStyle="min-width:10rem;">
                        <template #body="slotProps">
                            <Button v-if="!slotProps.data.approved" icon="pi pi-check" v-tooltip.top="'Approve'" class="mr-2" severity="success" rounded @click="approveTenant(slotProps.data)"></Button>
                            <Button icon="pi pi-users" v-tooltip.top="'Manage Users'" class="mr-2" severity="secondary" rounded @click="manageUsers(slotProps.data.id)"></Button>
                            <Button icon="pi pi-pencil" v-tooltip.top="'Edit'" class="mr-2" severity="info" rounded @click="editTenant(slotProps.data)"></Button>
                            <Button icon="pi pi-trash" v-tooltip.top="'Delete'" severity="danger" rounded @click="confirmDeleteTenant(slotProps.data)"></Button>
                        </template>
                    </Column>
                </DataTable>
            </div>

            <TenantForm :enhanced="true" :visible="tenantDialog" :tenant="tenant" @update:visible="tenantDialog = $event" @save="saveTenant" />

            <TenantUserForm :visible="adminUserDialog" :user="adminUser" @update:visible="adminUserDialog = $event" @save="saveAdminUser" />

            <Dialog v-model:visible="deleteTenantDialog" :style="{ width: '450px' }" header="Confirm" :modal="true">
                <div class="flex items-center">
                    <i class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem" />
                    <span v-if="tenant"
                        >Are you sure you want to delete <b>{{ tenant.name }}</b
                        >?</span
                    >
                </div>
                <template #footer>
                    <Button label="No" icon="pi pi-times" text @click="deleteTenantDialog = false"></Button>
                    <Button label="Yes" icon="pi pi-check" text @click="deleteTenant"></Button>
                </template>
            </Dialog>

            <Dialog v-model:visible="deleteTenantsDialog" :style="{ width: '450px' }" header="Confirm" :modal="true">
                <div class="flex items-center">
                    <i class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem" />
                    <span>Are you sure you want to delete the selected tenants?</span>
                </div>
                <template #footer>
                    <Button label="No" icon="pi pi-times" text @click="deleteTenantsDialog = false"></Button>
                    <Button label="Yes" icon="pi pi-check" text @click="deleteSelectedTenants"></Button>
                </template>
            </Dialog>
        </div>
    </div>
</template>
