<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useTenantStore } from '@/entities/tenant/store';
import { useAuthStore } from '@/entities/auth';
import { storeToRefs } from 'pinia';
import CompanyInfoForm from './components/CompanyInfoForm.vue';
import { CompanyInfoService } from './services/companyInfoService';
import type { CompanyInfo } from './types';

// Props
const props = defineProps<{
  tenantId?: string;
  readonly?: boolean;
  showHeader?: boolean;
}>();

// Stores
const toast = useToast();
const tenantStore = useTenantStore();
const authStore = useAuthStore();
const { isLoading } = storeToRefs(tenantStore);

// Local state
const companyInfo = ref<Partial<CompanyInfo>>({});
const isFormValid = ref(false);
const isSaving = ref(false);
const hasUnsavedChanges = ref(false);
const originalData = ref<Partial<CompanyInfo>>({});

// Computed properties
const currentTenantId = computed(() => {
  return props.tenantId || authStore.user?.tenantId || '';
});

const isReadonly = computed(() => {
  return props.readonly || false;
});

const showHeaderSection = computed(() => {
  return props.showHeader !== false;
});

// Methods
const loadCompanyInfo = async () => {
  if (!currentTenantId.value) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'No tenant ID available',
      life: 3000
    });
    return;
  }

  try {
    const data = await CompanyInfoService.getCompanyInfo(currentTenantId.value);
    companyInfo.value = { ...data };
    originalData.value = { ...data };
    hasUnsavedChanges.value = false;
  } catch (error) {
    console.error('Error loading company info:', error);
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to load company information',
      life: 3000
    });
  }
};

const saveCompanyInfo = async (data: Partial<CompanyInfo>) => {
  if (!currentTenantId.value) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'No tenant ID available',
      life: 3000
    });
    return;
  }

  isSaving.value = true;

  try {
    // Validate the data before saving
    const validation = await CompanyInfoService.validateCompanyInfo(data);

    if (!validation.isValid) {
      toast.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: validation.errors.join(', '),
        life: 5000
      });
      return;
    }

    // Convert to tenant format and save using existing updateTenant method
    const tenantData = CompanyInfoService.convertToTenantData(data);
    await tenantStore.updateTenant(currentTenantId.value, tenantData);

    // Update local state
    originalData.value = { ...data };
    hasUnsavedChanges.value = false;

    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Company information updated successfully',
      life: 3000
    });
  } catch (error) {
    console.error('Error saving company info:', error);
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to save company information',
      life: 3000
    });
  } finally {
    isSaving.value = false;
  }
};

const handleFormUpdate = (data: Partial<CompanyInfo>) => {
  companyInfo.value = { ...data };

  // Check if there are unsaved changes
  const hasChanges = JSON.stringify(data) !== JSON.stringify(originalData.value);
  hasUnsavedChanges.value = hasChanges;
};

const handleFormValidation = (isValid: boolean) => {
  isFormValid.value = isValid;
};

const resetForm = () => {
  companyInfo.value = { ...originalData.value };
  hasUnsavedChanges.value = false;
};

const confirmReset = () => {
  if (hasUnsavedChanges.value) {
    // Show confirmation dialog
    // For now, just reset directly
    resetForm();
    toast.add({
      severity: 'info',
      summary: 'Reset',
      detail: 'Form has been reset to original values',
      life: 3000
    });
  }
};

// Lifecycle
onMounted(() => {
  loadCompanyInfo();
});

// Expose methods for parent components
defineExpose({
  loadCompanyInfo,
  saveCompanyInfo,
  resetForm,
  hasUnsavedChanges: computed(() => hasUnsavedChanges.value),
  isFormValid: computed(() => isFormValid.value)
});
</script>

<template>
  <div class="company-info-update">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-8">
      <ProgressSpinner />
    </div>

    <!-- Main Content -->
    <div v-else class="space-y-6">
      <!-- Header Section (optional) -->
      <div v-if="showHeaderSection" class="bg-surface-card rounded-lg p-6 shadow-sm">
        <div class="flex justify-between items-start">
          <div>
            <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2">
              Company Settings
            </h1>
            <p class="text-surface-600 dark:text-surface-400 text-lg">
              Manage your company information and business details
            </p>
          </div>

          <!-- Action Buttons -->
          <div v-if="!isReadonly" class="flex gap-2">
            <Button
              v-if="hasUnsavedChanges"
              label="Reset"
              icon="pi pi-refresh"
              severity="secondary"
              outlined
              @click="confirmReset"
              :disabled="isSaving"
            />
            <Button
              label="Save Changes"
              icon="pi pi-save"
              severity="success"
              @click="saveCompanyInfo(companyInfo)"
              :loading="isSaving"
              :disabled="!isFormValid || !hasUnsavedChanges"
            />
          </div>
        </div>

        <!-- Unsaved Changes Indicator -->
        <div v-if="hasUnsavedChanges && !isReadonly" class="mt-4">
          <Message severity="warn" :closable="false">
            <div class="flex items-center gap-2">
              <i class="pi pi-exclamation-triangle"></i>
              <span>You have unsaved changes</span>
            </div>
          </Message>
        </div>
      </div>

      <!-- Company Info Form -->
      <div class="bg-surface-card rounded-lg shadow-sm">
        <CompanyInfoForm
          :company-info="companyInfo"
          :loading="isSaving"
          :readonly="isReadonly"
          @update:company-info="handleFormUpdate"
          @save="saveCompanyInfo"
          @validate="handleFormValidation"
        />
      </div>

      <!-- Footer Actions (for widget mode) -->
      <div v-if="!showHeaderSection && !isReadonly" class="flex justify-end gap-2 pt-4">
        <Button
          v-if="hasUnsavedChanges"
          label="Reset"
          icon="pi pi-refresh"
          severity="secondary"
          outlined
          @click="confirmReset"
          :disabled="isSaving"
        />
        <Button
          label="Save Changes"
          icon="pi pi-save"
          severity="success"
          @click="saveCompanyInfo(companyInfo)"
          :loading="isSaving"
          :disabled="!isFormValid || !hasUnsavedChanges"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.company-info-update {
  max-width: 1200px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .company-info-update {
    padding: 0 1rem;
  }
}
</style>
