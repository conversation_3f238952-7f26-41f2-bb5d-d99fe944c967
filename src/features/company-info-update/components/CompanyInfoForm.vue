<script setup lang="ts">
import { ref, watch, computed, defineProps, defineEmits } from 'vue';
import type { CompanyInfo, CompanyInfoErrors } from '../types';
import {
  validateEmail,
  validateRequired,
  validatePhoneNumber,
  validateUrl,
  validatePostalCode,
  validateCompanyNumber,
  validateVatNumber,
  validateNumber
} from '@/utils';
import {
  INDUSTRY_OPTIONS,
  COMPANY_SIZE_OPTIONS,
  COUNTRY_OPTIONS,
  STATUS_OPTIONS,
  SUBSCRIPTION_PLAN_OPTIONS
} from '../types';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';

// Component props
const props = defineProps<{
  companyInfo: Partial<CompanyInfo>;
  loading?: boolean;
  readonly?: boolean;
}>();

// Component emits
const emits = defineEmits<{
  'update:companyInfo': [value: Partial<CompanyInfo>];
  'save': [value: Partial<CompanyInfo>];
  'validate': [isValid: boolean];
}>();

// Local state
const internalCompanyInfo = ref<Partial<CompanyInfo>>({});
const submitted = ref(false);
const activeTab = ref(0);

// Initialize errors object
const errors = ref<Partial<CompanyInfoErrors>>({
  name: '',
  email: '',
  phone: '',
  address: '',
  website: '',
  industry: '',
  description: '',
  companyRegistrationNumber: '',
  vatNumber: '',
  city: '',
  state: '',
  postalCode: '',
  country: '',
  alternativePhone: '',
  supportEmail: '',
  linkedinUrl: '',
  twitterUrl: '',
  facebookUrl: '',
  foundedYear: ''
});

// Watch for changes in the companyInfo prop
watch(
  () => props.companyInfo,
  (newVal) => {
    internalCompanyInfo.value = { ...newVal };
    submitted.value = false;
  },
  { deep: true, immediate: true }
);

// Watch for changes in internal data and emit updates
watch(
  internalCompanyInfo,
  (newVal) => {
    emits('update:companyInfo', newVal);
  },
  { deep: true }
);

// Validation functions
const validateField = (field: keyof CompanyInfoErrors, value: string, validator: (val: string) => string) => {
  const error = validator(value);
  errors.value[field] = error;
  return !error;
};

const validateRequiredField = (field: keyof CompanyInfoErrors, value: string) => {
  return validateField(field, value, validateRequired);
};

const validateEmailField = (field: keyof CompanyInfoErrors, value: string) => {
  if (!value) {
    errors.value[field] = '';
    return true;
  }
  return validateField(field, value, validateEmail);
};

const validatePhoneField = (field: keyof CompanyInfoErrors, value: string) => {
  if (!value) {
    errors.value[field] = '';
    return true;
  }
  return validateField(field, value, validatePhoneNumber);
};

const validateUrlField = (field: keyof CompanyInfoErrors, value: string) => {
  if (!value) {
    errors.value[field] = '';
    return true;
  }
  return validateField(field, value, validateUrl);
};

const validateForm = () => {
  const validations = [
    validateRequiredField('name', internalCompanyInfo.value.name || ''),
    validateRequiredField('email', internalCompanyInfo.value.email || ''),
    validateEmailField('email', internalCompanyInfo.value.email || ''),
    validatePhoneField('phone', internalCompanyInfo.value.phone || ''),
    validateUrlField('website', internalCompanyInfo.value.website || ''),
    validateUrlField('linkedinUrl', internalCompanyInfo.value.linkedinUrl || ''),
    validateUrlField('twitterUrl', internalCompanyInfo.value.twitterUrl || ''),
    validateUrlField('facebookUrl', internalCompanyInfo.value.facebookUrl || ''),
    validateEmailField('supportEmail', internalCompanyInfo.value.supportEmail || ''),
    validatePhoneField('alternativePhone', internalCompanyInfo.value.alternativePhone || '')
  ];

  // Validate postal code if provided
  if (internalCompanyInfo.value.postalCode) {
    validations.push(validateField('postalCode', internalCompanyInfo.value.postalCode, validatePostalCode));
  }

  // Validate company registration number if provided
  if (internalCompanyInfo.value.companyRegistrationNumber) {
    validations.push(validateField('companyRegistrationNumber', internalCompanyInfo.value.companyRegistrationNumber, validateCompanyNumber));
  }

  // Validate VAT number if provided
  if (internalCompanyInfo.value.vatNumber) {
    validations.push(validateField('vatNumber', internalCompanyInfo.value.vatNumber, validateVatNumber));
  }

  // Validate founded year if provided
  if (internalCompanyInfo.value.foundedYear) {
    const currentYear = new Date().getFullYear();
    const yearStr = internalCompanyInfo.value.foundedYear.toString();
    const yearError = validateNumber(yearStr);
    if (yearError) {
      errors.value.foundedYear = yearError;
      validations.push(false);
    } else {
      const year = parseInt(yearStr);
      if (year < 1800 || year > currentYear) {
        errors.value.foundedYear = `Please enter a year between 1800 and ${currentYear}.`;
        validations.push(false);
      } else {
        errors.value.foundedYear = '';
        validations.push(true);
      }
    }
  }

  const isValid = validations.every(Boolean);
  emits('validate', isValid);
  return isValid;
};

// Handle form submission
const handleSave = () => {
  submitted.value = true;
  if (validateForm()) {
    emits('save', internalCompanyInfo.value);
  }
};

// Computed property to check if form has any errors
const hasErrors = computed(() => {
  return Object.values(errors.value).some(error => error !== '');
});

// Tab items for organizing the form
const tabItems = [
  { label: 'Basic Information', icon: 'pi pi-building' },
  { label: 'Contact Details', icon: 'pi pi-phone' },
  { label: 'Business Details', icon: 'pi pi-briefcase' },
  { label: 'Online Presence', icon: 'pi pi-globe' }
];

// Expose validation method
defineExpose({
  validateForm,
  hasErrors
});
</script>

<template>
  <div class="company-info-form">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-2xl font-bold text-surface-900 dark:text-surface-0 mb-2">
          Company Information
        </h2>
        <p class="text-surface-600 dark:text-surface-400">
          Update your company details and business information
        </p>
      </div>
      <div class="flex gap-2">
        <Button
          label="Save Changes"
          icon="pi pi-check"
          @click="handleSave"
          :loading="loading"
          :disabled="readonly || hasErrors"
          severity="success"
        />
      </div>
    </div>

    <!-- Tab Navigation -->
    <TabView v-model:activeIndex="activeTab" class="company-info-tabs">
      <!-- Basic Information Tab -->
      <TabPanel>
        <template #header>
          <div class="flex items-center gap-2">
            <i class="pi pi-building"></i>
            <span>Basic Information</span>
          </div>
        </template>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Company Name -->
          <div class="field">
            <label for="name" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Company Name <span class="text-red-500">*</span>
            </label>
            <IconField>
              <InputIcon class="pi pi-building" />
              <InputText
                id="name"
                v-model.trim="internalCompanyInfo.name"
                :class="{ 'p-invalid': errors.name && submitted }"
                :readonly="readonly"
                placeholder="Enter company name"
                fluid
              />
            </IconField>
            <small class="p-error" v-if="errors.name && submitted">{{ errors.name }}</small>
          </div>

          <!-- Industry -->
          <div class="field">
            <label for="industry" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Industry
            </label>
            <IconField>
              <InputIcon class="pi pi-briefcase" />
              <Dropdown
                id="industry"
                v-model="internalCompanyInfo.industry"
                :options="INDUSTRY_OPTIONS"
                optionLabel="label"
                optionValue="value"
                :readonly="readonly"
                placeholder="Select industry"
                fluid
              />
            </IconField>
          </div>

          <!-- Company Size -->
          <div class="field">
            <label for="companySize" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Company Size
            </label>
            <IconField>
              <InputIcon class="pi pi-users" />
              <Dropdown
                id="companySize"
                v-model="internalCompanyInfo.companySize"
                :options="COMPANY_SIZE_OPTIONS"
                optionLabel="label"
                optionValue="value"
                :readonly="readonly"
                placeholder="Select company size"
                fluid
              />
            </IconField>
          </div>

          <!-- Founded Year -->
          <div class="field">
            <label for="foundedYear" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Founded Year
            </label>
            <IconField>
              <InputIcon class="pi pi-calendar" />
              <InputNumber
                id="foundedYear"
                v-model="internalCompanyInfo.foundedYear"
                :class="{ 'p-invalid': errors.foundedYear && submitted }"
                :readonly="readonly"
                placeholder="e.g., 2020"
                :useGrouping="false"
                :min="1800"
                :max="new Date().getFullYear()"
                fluid
              />
            </IconField>
            <small class="p-error" v-if="errors.foundedYear && submitted">{{ errors.foundedYear }}</small>
          </div>
        </div>

        <!-- Description -->
        <div class="field mt-6">
          <label for="description" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
            Company Description
          </label>
          <Textarea
            id="description"
            v-model="internalCompanyInfo.description"
            :readonly="readonly"
            placeholder="Brief description of your company..."
            rows="4"
            fluid
          />
        </div>
      </TabPanel>

      <!-- Contact Details Tab -->
      <TabPanel>
        <template #header>
          <div class="flex items-center gap-2">
            <i class="pi pi-phone"></i>
            <span>Contact Details</span>
          </div>
        </template>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Email -->
          <div class="field">
            <label for="email" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Primary Email <span class="text-red-500">*</span>
            </label>
            <IconField>
              <InputIcon class="pi pi-envelope" />
              <InputText
                id="email"
                v-model.trim="internalCompanyInfo.email"
                :class="{ 'p-invalid': errors.email && submitted }"
                :readonly="readonly"
                placeholder="<EMAIL>"
                type="email"
                fluid
              />
            </IconField>
            <small class="p-error" v-if="errors.email && submitted">{{ errors.email }}</small>
          </div>

          <!-- Support Email -->
          <div class="field">
            <label for="supportEmail" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Support Email
            </label>
            <IconField>
              <InputIcon class="pi pi-headphones" />
              <InputText
                id="supportEmail"
                v-model.trim="internalCompanyInfo.supportEmail"
                :class="{ 'p-invalid': errors.supportEmail && submitted }"
                :readonly="readonly"
                placeholder="<EMAIL>"
                type="email"
                fluid
              />
            </IconField>
            <small class="p-error" v-if="errors.supportEmail && submitted">{{ errors.supportEmail }}</small>
          </div>

          <!-- Phone -->
          <div class="field">
            <label for="phone" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Primary Phone
            </label>
            <IconField>
              <InputIcon class="pi pi-phone" />
              <InputText
                id="phone"
                v-model.trim="internalCompanyInfo.phone"
                :class="{ 'p-invalid': errors.phone && submitted }"
                :readonly="readonly"
                placeholder="+44 20 1234 5678"
                fluid
              />
            </IconField>
            <small class="p-error" v-if="errors.phone && submitted">{{ errors.phone }}</small>
          </div>

          <!-- Alternative Phone -->
          <div class="field">
            <label for="alternativePhone" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Alternative Phone
            </label>
            <IconField>
              <InputIcon class="pi pi-mobile" />
              <InputText
                id="alternativePhone"
                v-model.trim="internalCompanyInfo.alternativePhone"
                :class="{ 'p-invalid': errors.alternativePhone && submitted }"
                :readonly="readonly"
                placeholder="+44 7123 456789"
                fluid
              />
            </IconField>
            <small class="p-error" v-if="errors.alternativePhone && submitted">{{ errors.alternativePhone }}</small>
          </div>
        </div>

        <!-- Address Section -->
        <Divider align="left">
          <div class="inline-flex items-center">
            <i class="pi pi-map-marker mr-2"></i>
            <b>Address Information</b>
          </div>
        </Divider>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Address Line 1 -->
          <div class="field md:col-span-2">
            <label for="address" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Address Line 1
            </label>
            <IconField>
              <InputIcon class="pi pi-map-marker" />
              <InputText
                id="address"
                v-model.trim="internalCompanyInfo.address"
                :readonly="readonly"
                placeholder="Street address"
                fluid
              />
            </IconField>
          </div>

          <!-- Address Line 2 -->
          <div class="field md:col-span-2">
            <label for="addressLine2" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Address Line 2
            </label>
            <InputText
              id="addressLine2"
              v-model.trim="internalCompanyInfo.addressLine2"
              :readonly="readonly"
              placeholder="Apartment, suite, etc. (optional)"
              fluid
            />
          </div>

          <!-- City -->
          <div class="field">
            <label for="city" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              City
            </label>
            <InputText
              id="city"
              v-model.trim="internalCompanyInfo.city"
              :readonly="readonly"
              placeholder="City"
              fluid
            />
          </div>

          <!-- State/Province -->
          <div class="field">
            <label for="state" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              State/Province
            </label>
            <InputText
              id="state"
              v-model.trim="internalCompanyInfo.state"
              :readonly="readonly"
              placeholder="State or Province"
              fluid
            />
          </div>

          <!-- Postal Code -->
          <div class="field">
            <label for="postalCode" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Postal Code
            </label>
            <InputText
              id="postalCode"
              v-model.trim="internalCompanyInfo.postalCode"
              :class="{ 'p-invalid': errors.postalCode && submitted }"
              :readonly="readonly"
              placeholder="Postal code"
              fluid
            />
            <small class="p-error" v-if="errors.postalCode && submitted">{{ errors.postalCode }}</small>
          </div>

          <!-- Country -->
          <div class="field">
            <label for="country" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Country
            </label>
            <Dropdown
              id="country"
              v-model="internalCompanyInfo.country"
              :options="COUNTRY_OPTIONS"
              optionLabel="label"
              optionValue="value"
              :readonly="readonly"
              placeholder="Select country"
              fluid
            />
          </div>
        </div>
      </TabPanel>

      <!-- Business Details Tab -->
      <TabPanel>
        <template #header>
          <div class="flex items-center gap-2">
            <i class="pi pi-briefcase"></i>
            <span>Business Details</span>
          </div>
        </template>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Company Registration Number -->
          <div class="field">
            <label for="companyRegistrationNumber" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Company Registration Number
            </label>
            <IconField>
              <InputIcon class="pi pi-id-card" />
              <InputText
                id="companyRegistrationNumber"
                v-model.trim="internalCompanyInfo.companyRegistrationNumber"
                :class="{ 'p-invalid': errors.companyRegistrationNumber && submitted }"
                :readonly="readonly"
                placeholder="e.g., 12345678"
                fluid
              />
            </IconField>
            <small class="p-error" v-if="errors.companyRegistrationNumber && submitted">{{ errors.companyRegistrationNumber }}</small>
          </div>

          <!-- VAT Number -->
          <div class="field">
            <label for="vatNumber" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              VAT Number
            </label>
            <IconField>
              <InputIcon class="pi pi-percentage" />
              <InputText
                id="vatNumber"
                v-model.trim="internalCompanyInfo.vatNumber"
                :class="{ 'p-invalid': errors.vatNumber && submitted }"
                :readonly="readonly"
                placeholder="e.g., GB123456789"
                fluid
              />
            </IconField>
            <small class="p-error" v-if="errors.vatNumber && submitted">{{ errors.vatNumber }}</small>
          </div>

          <!-- Status -->
          <div class="field">
            <label for="status" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Status
            </label>
            <IconField>
              <InputIcon class="pi pi-circle" />
              <Dropdown
                id="status"
                v-model="internalCompanyInfo.status"
                :options="STATUS_OPTIONS"
                optionLabel="label"
                optionValue="value"
                :readonly="readonly"
                placeholder="Select status"
                fluid
              />
            </IconField>
          </div>

          <!-- Subscription Plan -->
          <div class="field">
            <label for="subscriptionPlan" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Subscription Plan
            </label>
            <IconField>
              <InputIcon class="pi pi-credit-card" />
              <Dropdown
                id="subscriptionPlan"
                v-model="internalCompanyInfo.subscriptionPlan"
                :options="SUBSCRIPTION_PLAN_OPTIONS"
                optionLabel="label"
                optionValue="value"
                :readonly="readonly"
                placeholder="Select plan"
                fluid
              />
            </IconField>
          </div>

          <!-- Timezone -->
          <div class="field md:col-span-2">
            <label for="timezone" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Timezone
            </label>
            <IconField>
              <InputIcon class="pi pi-clock" />
              <InputText
                id="timezone"
                v-model.trim="internalCompanyInfo.timezone"
                :readonly="readonly"
                placeholder="e.g., Europe/London"
                fluid
              />
            </IconField>
          </div>
        </div>
      </TabPanel>

      <!-- Online Presence Tab -->
      <TabPanel>
        <template #header>
          <div class="flex items-center gap-2">
            <i class="pi pi-globe"></i>
            <span>Online Presence</span>
          </div>
        </template>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Website -->
          <div class="field md:col-span-2">
            <label for="website" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Website
            </label>
            <IconField>
              <InputIcon class="pi pi-globe" />
              <InputText
                id="website"
                v-model.trim="internalCompanyInfo.website"
                :class="{ 'p-invalid': errors.website && submitted }"
                :readonly="readonly"
                placeholder="https://www.example.com"
                type="url"
                fluid
              />
            </IconField>
            <small class="p-error" v-if="errors.website && submitted">{{ errors.website }}</small>
          </div>

          <!-- LinkedIn URL -->
          <div class="field">
            <label for="linkedinUrl" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              LinkedIn Profile
            </label>
            <IconField>
              <InputIcon class="pi pi-linkedin" />
              <InputText
                id="linkedinUrl"
                v-model.trim="internalCompanyInfo.linkedinUrl"
                :class="{ 'p-invalid': errors.linkedinUrl && submitted }"
                :readonly="readonly"
                placeholder="https://linkedin.com/company/example"
                type="url"
                fluid
              />
            </IconField>
            <small class="p-error" v-if="errors.linkedinUrl && submitted">{{ errors.linkedinUrl }}</small>
          </div>

          <!-- Twitter URL -->
          <div class="field">
            <label for="twitterUrl" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Twitter Profile
            </label>
            <IconField>
              <InputIcon class="pi pi-twitter" />
              <InputText
                id="twitterUrl"
                v-model.trim="internalCompanyInfo.twitterUrl"
                :class="{ 'p-invalid': errors.twitterUrl && submitted }"
                :readonly="readonly"
                placeholder="https://twitter.com/example"
                type="url"
                fluid
              />
            </IconField>
            <small class="p-error" v-if="errors.twitterUrl && submitted">{{ errors.twitterUrl }}</small>
          </div>

          <!-- Facebook URL -->
          <div class="field md:col-span-2">
            <label for="facebookUrl" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0">
              Facebook Page
            </label>
            <IconField>
              <InputIcon class="pi pi-facebook" />
              <InputText
                id="facebookUrl"
                v-model.trim="internalCompanyInfo.facebookUrl"
                :class="{ 'p-invalid': errors.facebookUrl && submitted }"
                :readonly="readonly"
                placeholder="https://facebook.com/example"
                type="url"
                fluid
              />
            </IconField>
            <small class="p-error" v-if="errors.facebookUrl && submitted">{{ errors.facebookUrl }}</small>
          </div>
        </div>
      </TabPanel>
    </TabView>
  </div>
</template>

<style scoped>
.company-info-form {
  max-width: 1200px;
  margin: 0 auto;
}

.company-info-tabs :deep(.p-tabview-nav) {
  background: var(--surface-card);
  border-radius: 12px 12px 0 0;
}

.company-info-tabs :deep(.p-tabview-panels) {
  background: var(--surface-card);
  border-radius: 0 0 12px 12px;
  padding: 2rem;
}

.field {
  margin-bottom: 1.5rem;
}

.p-error {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .grid-cols-1.md\\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
</style>
