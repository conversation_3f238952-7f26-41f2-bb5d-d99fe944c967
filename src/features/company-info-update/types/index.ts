import type { Tenant } from '@/entities/tenant/model';

/**
 * Extended company information interface for SaaS products
 */
export interface CompanyInfo extends Omit<Tenant, 'id' | 'ownerId' | 'approved' | 'createdAt' | 'updatedAt'> {
  // Core company details
  name: string;
  email: string;
  phone: string;
  address: string;
  website?: string;
  
  // Additional SaaS-specific fields
  industry?: string;
  companySize?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  description?: string;
  
  // Business registration details
  companyRegistrationNumber?: string;
  vatNumber?: string;
  
  // Address details
  addressLine2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  
  // Contact details
  alternativePhone?: string;
  supportEmail?: string;
  
  // Social media and online presence
  linkedinUrl?: string;
  twitterUrl?: string;
  facebookUrl?: string;
  
  // Business details
  foundedYear?: number;
  timezone?: string;
  
  // Subscription and status (inherited from Tenant)
  status: 'active' | 'inactive' | 'trial';
  subscriptionPlan: 'free' | 'basic' | 'premium';
}

/**
 * Form validation errors interface
 */
export interface CompanyInfoErrors {
  name: string;
  email: string;
  phone: string;
  address: string;
  website: string;
  industry: string;
  description: string;
  companyRegistrationNumber: string;
  vatNumber: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  alternativePhone: string;
  supportEmail: string;
  linkedinUrl: string;
  twitterUrl: string;
  facebookUrl: string;
  foundedYear: string;
}

/**
 * Industry options for dropdown
 */
export const INDUSTRY_OPTIONS = [
  { label: 'Technology', value: 'technology' },
  { label: 'Healthcare', value: 'healthcare' },
  { label: 'Finance', value: 'finance' },
  { label: 'Education', value: 'education' },
  { label: 'Retail', value: 'retail' },
  { label: 'Manufacturing', value: 'manufacturing' },
  { label: 'Real Estate', value: 'real-estate' },
  { label: 'Consulting', value: 'consulting' },
  { label: 'Marketing', value: 'marketing' },
  { label: 'Legal', value: 'legal' },
  { label: 'Non-profit', value: 'non-profit' },
  { label: 'Other', value: 'other' }
];

/**
 * Company size options for dropdown
 */
export const COMPANY_SIZE_OPTIONS = [
  { label: 'Startup (1-10 employees)', value: 'startup' },
  { label: 'Small (11-50 employees)', value: 'small' },
  { label: 'Medium (51-200 employees)', value: 'medium' },
  { label: 'Large (201-1000 employees)', value: 'large' },
  { label: 'Enterprise (1000+ employees)', value: 'enterprise' }
];

/**
 * Country options for dropdown (common countries)
 */
export const COUNTRY_OPTIONS = [
  { label: 'United Kingdom', value: 'GB' },
  { label: 'United States', value: 'US' },
  { label: 'Canada', value: 'CA' },
  { label: 'Australia', value: 'AU' },
  { label: 'Germany', value: 'DE' },
  { label: 'France', value: 'FR' },
  { label: 'Netherlands', value: 'NL' },
  { label: 'Ireland', value: 'IE' },
  { label: 'Spain', value: 'ES' },
  { label: 'Italy', value: 'IT' },
  { label: 'Other', value: 'other' }
];

/**
 * Status options for dropdown
 */
export const STATUS_OPTIONS = [
  { label: 'Active', value: 'active' },
  { label: 'Trial', value: 'trial' },
  { label: 'Inactive', value: 'inactive' }
];

/**
 * Subscription plan options for dropdown
 */
export const SUBSCRIPTION_PLAN_OPTIONS = [
  { label: 'Free', value: 'free' },
  { label: 'Basic', value: 'basic' },
  { label: 'Premium', value: 'premium' }
];
